{"_checkpoint": {"global_record": {"index": -1, "route_id": -1, "status": "Failed", "infractions": {"collisions_layout": 0.0, "collisions_pedestrian": 0.0, "collisions_vehicle": 0.0, "red_light": 0.0, "stop_infraction": 0.0, "outside_route_lanes": 0.0, "min_speed_infractions": 0.0, "yield_emergency_vehicle_infractions": 0.0, "scenario_timeouts": 0.0, "route_dev": 0.0, "vehicle_blocked": 0.0, "route_timeout": 0.0}, "scores_mean": {"score_composed": 0.0, "score_route": 0.0, "score_penalty": 1.0}, "scores_std_dev": {"score_composed": 0.0, "score_route": 0.0, "score_penalty": 0.0}, "meta": {"total_length": 328.557, "duration_game": 0.0, "duration_system": 0.0, "exceptions": [["RouteScenario_09_rep0", 0, "Failed - Agent couldn't be set up"], ["RouteScenario_07_rep0", 1, "Failed - Agent couldn't be set up"]]}}, "progress": [2, 2], "records": [{"index": 0, "route_id": "RouteScenario_09_rep0", "status": "Failed - Agent couldn't be set up", "num_infractions": 0, "infractions": {"collisions_layout": [], "collisions_pedestrian": [], "collisions_vehicle": [], "red_light": [], "stop_infraction": [], "outside_route_lanes": [], "min_speed_infractions": [], "yield_emergency_vehicle_infractions": [], "scenario_timeouts": [], "route_dev": [], "vehicle_blocked": [], "route_timeout": []}, "scores": {"score_route": 0, "score_penalty": 1.0, "score_composed": 0.0}, "meta": {"route_length": 99.8, "duration_game": 0.0, "duration_system": 0.0}}, {"index": 1, "route_id": "RouteScenario_07_rep0", "status": "Failed - Agent couldn't be set up", "num_infractions": 0, "infractions": {"collisions_layout": [], "collisions_pedestrian": [], "collisions_vehicle": [], "red_light": [], "stop_infraction": [], "outside_route_lanes": [], "min_speed_infractions": [], "yield_emergency_vehicle_infractions": [], "scenario_timeouts": [], "route_dev": [], "vehicle_blocked": [], "route_timeout": []}, "scores": {"score_route": 0, "score_penalty": 1.0, "score_composed": 0.0}, "meta": {"route_length": 228.757, "duration_game": 0.0, "duration_system": 0.0}}]}, "entry_status": "Finished", "eligible": true, "sensors": [], "values": ["0.0", "0.0", "1.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0"], "labels": ["Avg. driving score", "Avg. route completion", "Avg. infraction penalty", "Collisions with pedestrians", "Collisions with vehicles", "Collisions with layout", "Red lights infractions", "Stop sign infractions", "Off-road infractions", "Route deviations", "Route timeouts", "Agent blocked", "Yield emergency vehicles infractions", "Sc<PERSON>rio timeouts", "Min speed infractions"]}