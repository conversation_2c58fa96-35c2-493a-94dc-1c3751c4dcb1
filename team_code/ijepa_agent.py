import os
from copy import deepcopy

import torch
import torch.nn.functional as F
from torch.cuda.amp import autocast, GradScaler
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as backbone_models
from torch.utils.data import DataLoader
from tqdm import tqdm
import timm
from timm.models.layers import trunc_normal_
import torch.optim as optim

import cv2
import carla
from collections import deque
import numpy as np
import math
import sys
import wandb
from PIL import Image
import json
import logging

logger = logging.getLogger(__name__)


sys.path.append('/home/<USER>/work/research/world_models/vjepa2_wm/')
sys.path.append('/home/<USER>/work/research/world_models/vjepa2_wm/leaderboard/')
sys.path.append('/home/<USER>/work/research/world_models/vjepa2_wm/scenario_runner/')
print(sys.path)

from leaderboard.autoagents import autonomous_agent

from transformers import AutoProcessor, IJepaModel
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training, TaskType

import pdb
from src.data_loader.downstream_dataloader import CARLA_Data
from src.data_loader.downstream_config import GlobalConfig
from src.helper import init_model
from src.utils.logging import (
    CSVLogger,
    gpu_timer,
    grad_logger,
    AverageMeter)
from src.utils.tensors import repeat_interleave_batch
import argparse
import yaml

from src.downstream_task_train import CarlaNet



from nav_planner import RoutePlanner
from nav_planner import extrapolate_waypoint_route

from filterpy.kalman import MerweScaledSigmaPoints
from filterpy.kalman import UnscentedKalmanFilter as UKF
from scipy.optimize import fsolve

from scenario_logger import ScenarioLogger

from matplotlib import cm
import itertools
import pathlib

# Configure pytorch for maximum performance
torch.backends.cuda.matmul.allow_tf32 = True
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False
torch.backends.cudnn.allow_tf32 = True

print('import are done')
SAVE_PATH = os.environ.get('SAVE_PATH')

if not SAVE_PATH:
	SAVE_PATH = None
else:
	pathlib.Path(SAVE_PATH).mkdir(parents=True, exist_ok=True)

def get_entry_point():
	return 'CarlaNetAgent'


def strtobool(v):
  return str(v).lower() in ('yes', 'y', 'true', 't', '1', 'True')


class CarlaNetAgent(autonomous_agent.AutonomousAgent):
    """
    An autonomous agent for CARLA that uses CarlaNet for waypoint prediction and a PID controller for vehicle control.
    Integrates sensor data (RGB, LiDAR, GPS, IMU, speed) with state estimation (UKF) for robust navigation.
    """

    def setup(self, path_to_conf_file, route_index=None, traffic_manager=None):
        """
        Initializes the agent with configuration, model, and state estimation components.
        
        Parameters:
        - path_to_conf_file: Path to YAML configuration file
        - route_index: Optional route index (not used here)
        - traffic_manager: Optional traffic manager (not used here)
        
        Why Necessary:
        - Sets up all components (model, encoders, UKF, route planner) before the simulation begins.
        - Ensures the agent is configured with the correct parameters and pretrained weights.
        """
        torch.cuda.empty_cache()
        self.IS_BENCH2DRIVE = strtobool(os.environ.get('IS_BENCH2DRIVE', 'False'))
        print('IS_BENCH2DRIVE: ', self.IS_BENCH2DRIVE)
        self.track = autonomous_agent.Track.MAP if os.environ.get(
            'CHALLENGE_TRACK_CODENAME') == 'MAP' else autonomous_agent.Track.SENSORS
        if self.IS_BENCH2DRIVE:
            self.config_path = path_to_conf_file.split('+')[0]
        else:
            self.config_path = path_to_conf_file
        
        self.step = -1
        self.initialized = False
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        # pdb.set_trace()
        # Load configuration from YAML
        with open(path_to_conf_file, 'r') as f:
            # config_dict = yaml.safe_load(f)
            config_dict = json.load(f)
        config_dict['setting'] = 'eval'
        if config_dict['jepa_type'] == 'cnn_jepa':
            img_resolution = (config_dict['cnn']['cnn_data_crop'], config_dict['cnn']['cnn_data_crop'])
        elif config_dict['jepa_type'] == 'vit_jepa':
            img_resolution = (config_dict['vit']['vit_data_crop_size'], config_dict['vit']['vit_data_crop_size'])
        
        config_dict['img_resolution'] = img_resolution
        
        self.config = GlobalConfig(**config_dict)
        
        

        self.config.debug = int(os.environ.get('DEBUG_CHALLENGE', 0)) == 1
        
        self.compile = int(os.environ.get('COMPILE', 0)) == 1

        self.config.brake_uncertainty_threshold = float(
            os.environ.get('UNCERTAINTY_THRESHOLD', self.config.brake_uncertainty_threshold))
        print('Brake uncertainty threshold: ', self.config.brake_uncertainty_threshold)

        # Classification networks are known to be overconfident which leads to them braking a bit too late in our case.
        # Reducing the driving speed slightly counteracts that.
        if int(os.environ.get('SLOWER', 0)):
            print(f'Reduce target speeds during evaluation by factor {self.config.slower_factor}.')
            self.inference_target_speeds = [self.config.slower_factor * speed for speed in self.config.target_speeds]
        else:
            print('No speed reduction during inference.')
            self.inference_target_speeds = self.config.target_speeds

        # Stop signs can be occluded with our camera setup. This buffer remembers them until cleared.
        # Very useful on the LAV benchmark
        self.stop_sign_controller = int(os.environ.get('STOP_CONTROL', 1))
        print('Use stop sign controller:', self.stop_sign_controller)
        if self.stop_sign_controller:
            # There can be max 1 stop sign affecting the ego
            self.stop_sign_buffer = deque(maxlen=1)
            self.clear_stop_sign = 0  # Counter if we recently cleared a stop sign
        
        # Set default PID and simulation parameters if not in config
        defaults = [
            ('carla_fps', 20), ('wp_dilation', 1), ('data_save_freq', 1),
            ('brake_speed', 0.5), ('brake_ratio', 2.0), ('clip_delta', 1.0),
            ('clip_throttle', 1.0), ('turn_kp', 1.25), ('turn_ki', 0.75),
            ('turn_kd', 0.3), ('turn_n', 20), ('speed_kp', 5.0),
            ('speed_ki', 0.5), ('speed_kd', 1.0), ('speed_n', 20)
        ]
        for attr, default in defaults:
            if not hasattr(self.config, attr):
                setattr(self.config, attr, default)
        
        config_for_model = vars(self.config)
        # Initialize RGB and LiDAR encoders

        model_id = self.config.lora['ijepa_ID']
        jepa = IJepaModel.from_pretrained(model_id)
        lora_cfg = LoraConfig(
            task_type=TaskType.FEATURE_EXTRACTION,
            r=self.config.lora['r'],
            lora_alpha=self.config.lora['lora_alpha'],
            target_modules=["query", "key", "value"],
            lora_dropout=self.config.lora['lora_dropout'],
            bias="none"
        )
        self.encoder_rgb = get_peft_model(jepa, lora_cfg)
        self.encoder_rgb.to(self.device)

        # Initialize CarlaNet with the RGB encoder
        config_for_model = vars(self.config)
        self.model = CarlaNet(self.encoder_rgb, config_for_model, self.device)

        # Load the trained checkpoint
        checkpoint_path = os.path.join(self.config.checkpoint['downstream_stage_ckpt'], 'model_lora.pth')
        if os.path.exists(checkpoint_path):
            state_dict = torch.load(checkpoint_path, map_location=self.device)
            # Handle potential nn.DataParallel wrapping
            if list(state_dict.keys())[0].startswith('module.'):
                state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
            self.model.load_state_dict(state_dict, strict=False)
            logger.info(f"Loaded trained checkpoint from {checkpoint_path}")
        else:
            logger.error(f"Trained checkpoint not found at {checkpoint_path}")
            raise FileNotFoundError(f"Trained checkpoint not found at {checkpoint_path}")

        # Use DataParallel if multiple GPUs are available
        if torch.cuda.device_count() > 1:
            print(f"Using {torch.cuda.device_count()} GPUs!")
            self.model = nn.DataParallel(self.model)
        self.model.to(self.device)
        self.model.eval()


        self.stuck_detector = 0
        self.force_move = 0

        self.bb_buffer = deque(maxlen=1)
        self.commands = deque(maxlen=2)
        self.commands.append(4)
        self.commands.append(4)
        self.target_point_prev = [1e5, 1e5, 1e5]    

    
        # Initialize route planner and PID controller
        # self.route_planner = RoutePlanner(min_distance=2.0, max_distance=50.0)
        # self.route_planner = RoutePlanner(self.config.route_planner_min_distance, self.config.route_planner_max_distance,
        #                                self.lat_ref, self.lon_ref)
        # self._route_planner.set_route(self._global_plan, True)
        self.pid_controller = VehiclePIDController(self.config)

        # Setup UKF for state estimation
        self.ego_model = EgoModel(dt=self.config.carla_frame_rate)
        self.points = MerweScaledSigmaPoints(n=4, alpha=0.00001, beta=2, kappa=0, subtract=residual_state_x)
    # Still uses the leaderboard 1.0 bicycle model for the unscented kalman filter
        self.ukf = UKF(dim_x=4,
                    dim_z=4,
                    fx=bicycle_model_forward,
                    hx=measurement_function_hx,
                    dt=self.config.carla_frame_rate,
                    points=self.points,
                    x_mean_fn=state_mean,
                    z_mean_fn=measurement_mean,
                    residual_x=residual_state_x,
                    residual_z=residual_measurement_h)
        
        self.ukf.P = np.diag([0.5, 0.5, 0.000001, 0.000001])  # Initial state covariance
        self.ukf.R = np.diag([0.5, 0.5, 1e-12, 1e-12])        # Measurement noise covariance
        self.ukf.Q = np.diag([0.0001, 0.0001, 0.001, 0.001])  # Process noise covariance
        self.filter_initialized = False
        self.state_log = deque(maxlen=max((self.config.lidar_seq_len * self.config.data_save_freq), 2))
        # Log filtered states for debugging or analysis
        
        self.lidar_buffer = deque(maxlen=self.config.lidar_seq_len * self.config.data_save_freq)
        self.lidar_last = None
        #         # Forced stopping
        # if self.stop_after_meter > 0:
        #     self.meters_travelled = 0

        self.data = CARLA_Data(root=[], config=self.config, shared_dict=None)

        # Path to where visualizations and other debug output gets stored
        self.save_path = os.environ.get('SAVE_PATH', None)

        # Logger that generates logs used for infraction replay in the results_parser.
        if self.save_path is not None and route_index is not None:
            self.save_path = pathlib.Path(self.save_path) / route_index
            pathlib.Path(self.save_path).mkdir(parents=True, exist_ok=True)

            self.lon_logger = ScenarioLogger(
                save_path=self.save_path,
                route_index=route_index,
                logging_freq=self.config.logging_freq,
                log_only=True,
                route_only=False,  # with vehicles
                roi=self.config.logger_region_of_interest,
            )
        else:
            self.save_path = None

        self.metric_info = {}

    def sensors(self):        
        sensors = [{
            'type': 'sensor.camera.rgb',
            'x': self.config.camera_pos[0],
            'y': self.config.camera_pos[1],
            'z': self.config.camera_pos[2],
            'roll': self.config.camera_rot_0[0],
            'pitch': self.config.camera_rot_0[1],
            'yaw': self.config.camera_rot_0[2],
            'width': self.config.camera_width,
            'height': self.config.camera_height,
            'fov': self.config.camera_fov,
            'id': 'rgb_front'
        }, {
            'type': 'sensor.other.imu',
            'x': 0.0,
            'y': 0.0,
            'z': 0.0,
            'roll': 0.0,
            'pitch': 0.0,
            'yaw': 0.0,
            'sensor_tick': self.config.carla_frame_rate,
            'id': 'imu'
        }, {
            'type': 'sensor.other.gnss',
            'x': 0.0,
            'y': 0.0,
            'z': 0.0,
            'roll': 0.0,
            'pitch': 0.0,
            'yaw': 0.0,
            'sensor_tick': 0.01,
            'id': 'gps'
        }, {
            'type': 'sensor.speedometer',
            'reading_frequency': self.config.carla_fps,
            'id': 'speed'
        }, {
            'type': 'sensor.lidar.ray_cast',
            'x': self.config.lidar_pos[0],
            'y': self.config.lidar_pos[1],
            'z': self.config.lidar_pos[2],
            'roll': self.config.lidar_rot[0],
            'pitch': self.config.lidar_rot[1],
            'yaw': self.config.lidar_rot[2],
            'id': 'lidar'
        }]
        return sensors        

    def _init(self):
        """
        Performs additional initialization once sensor data and global plan are available.
        
        Why Necessary:
        - Estimates GPS reference coordinates (lat_ref, lon_ref) to align GPS and CARLA coordinate systems.
        - Initializes the route planner with the global plan and reference coordinates.
        """
        # Estimate GPS reference using the first waypoint
        try:
            locx, locy = self._global_plan_world_coord[0][0].location.x, self._global_plan_world_coord[0][0].location.y
            lon, lat = self._global_plan[0][0]['lon'], self._global_plan[0][0]['lat']
            earth_radius_equa = 6378137.0  # Earth radius in meters

            def equations(variables):
                x, y = variables  # lat_ref, lon_ref
                eq1 = (lon * math.cos(x * math.pi / 180.0) - (locx * x * 180.0) / (math.pi * earth_radius_equa) -
                       math.cos(x * math.pi / 180.0) * y)
                eq2 = (math.log(math.tan((lat + 90.0) * math.pi / 360.0)) * earth_radius_equa * math.cos(x * math.pi / 180.0) +
                       locy - math.cos(x * math.pi / 180.0) * earth_radius_equa * math.log(math.tan((90.0 + x) * math.pi / 360.0)))
                return [eq1, eq2]

            initial_guess = [0.0, 0.0]
            solution = fsolve(equations, initial_guess)
            self.lat_ref, self.lon_ref = solution[0], solution[1]
        except Exception as e:
            print(f"GPS reference estimation failed: {e}", flush=True)
            self.lat_ref, self.lon_ref = 0.0, 0.0

        # Initialize route planner with estimated references
        self.route_planner = RoutePlanner(self.config.route_planner_min_distance, self.config.route_planner_max_distance,
                                       self.lat_ref, self.lon_ref)
        self.route_planner.set_route(self._global_plan, True)
        self.initialized = True

    def preprocess_rgb(self, rgb):
        """
        Preprocesses RGB image data for model input.
        
        Parameters:
        - rgb: Raw RGB image from CARLA (BGR format)
        
        Returns:
        - Preprocessed tensor in CHW format
        
        Why Necessary:
        - Converts BGR to RGB, resizes, normalizes, and transposes the image to match the model’s expected input format.
        """
        rgb = cv2.cvtColor(rgb, cv2.COLOR_BGR2RGB)  # Convert to RGB
        rgb = cv2.resize(rgb, self.config.img_resolution)  # Resize to model resolution
        rgb = rgb.transpose(2, 0, 1) / 255.0  # CHW format and normalize to [0, 1]
        return rgb.astype(np.float32)

    def preprocess_lidar(self, lidar):
        """
        Converts LiDAR point cloud to a Bird’s Eye View (BEV) tensor with three channels.
        
        Parameters:
        - lidar: Raw LiDAR data (point cloud), shape (N, 4) with [x, y, z, intensity]
        
        Returns:
        - BEV tensor of shape (1, 3, 256, 256) with channels for density, height, and intensity
        
        Why Necessary:
        - Projects 3D LiDAR points into a 2D grid with multiple feature channels to match the
        input requirements of the CarlaNet model, which expects a [1, 3, 256, 256] tensor.
        - Simplifies spatial data into a format suitable for CNN processing.
        """
        points = lidar[:, :3]  # Extract x, y, z coordinates
        intensities = lidar[:, 3]  # Extract intensity values
        height, width = 256, 256  # Fixed resolution to match model input
        
        # Initialize three channels: density, height, intensity
        bev_density = np.zeros((height, width), dtype=np.float32)  # Point count
        bev_height = np.zeros((height, width), dtype=np.float32)   # Max z-coordinate
        bev_intensity = np.zeros((height, width), dtype=np.float32)  # Sum of intensities
        count = np.zeros((height, width), dtype=np.float32)        # Count for averaging intensity
        
        # Define BEV spatial bounds (40x40 meters)
        x_min, x_max, y_min, y_max = -20.0, 20.0, -20.0, 20.0
        
        # Project points to BEV grid
        for i, (x, y, z) in enumerate(points):
            if x_min < x < x_max and y_min < y < y_max:
                # Map x, y to grid indices
                x_idx = int((x - x_min) / (x_max - x_min) * width)
                y_idx = int((y - y_min) / (y_max - y_min) * height)
                
                # Ensure indices are within bounds
                if 0 <= x_idx < width and 0 <= y_idx < height:
                    bev_density[y_idx, x_idx] += 1.0  # Increment density
                    bev_height[y_idx, x_idx] = max(bev_height[y_idx, x_idx], z)  # Update max height
                    bev_intensity[y_idx, x_idx] += intensities[i]  # Sum intensity
                    count[y_idx, x_idx] += 1.0  # Increment count for intensity averaging
        
        # Normalize intensity channel (average intensity where points exist)
        valid_mask = count > 0
        bev_intensity[valid_mask] /= count[valid_mask]
        bev_intensity[~valid_mask] = 0.0  # Set empty cells to 0
        
        # Normalize channels to [0, 1] for model input
        # Density: Cap at a reasonable max (e.g., 50 points) to avoid outliers
        bev_density = np.clip(bev_density / 50.0, 0.0, 1.0)
        # Height: Assume z in [-2, 5] meters (adjust based on your data)
        bev_height = np.clip((bev_height + 2.0) / 7.0, 0.0, 1.0)
        # Intensity: Assume intensity in [0, 1] (CARLA typically normalizes)
        bev_intensity = np.clip(bev_intensity, 0.0, 1.0)
        
        # Stack channels and add batch dimension
        bev = np.stack([bev_density, bev_height, bev_intensity], axis=0)  # Shape: (3, 256, 256)
        bev = bev[np.newaxis, :, :, :]  # Shape: (1, 3, 256, 256)
        
        return bev.astype(np.float32)

    def convert_to_ego_coordinates(self, target_point, gps, compass):
        """
        Converts a target point from world to ego-vehicle coordinates.
        
        Parameters:
        - target_point: [x, y] in world coordinates
        - gps: Current vehicle position [x, y]
        - compass: Current vehicle yaw angle
        
        Returns:
        - Target point in ego coordinates
        
        Why Necessary:
        - The model expects waypoints relative to the vehicle’s frame, not the world frame.
        - Applies rotation and translation based on vehicle orientation and position.
        """
        target_x, target_y = target_point[0] - gps[0], target_point[1] - gps[1]
        cos_yaw, sin_yaw = np.cos(compass), np.sin(compass)
        ego_x = target_x * cos_yaw + target_y * sin_yaw
        ego_y = -target_x * sin_yaw + target_y * cos_yaw
        return np.array([ego_x, ego_y])

    @torch.inference_mode()
    def tick(self, input_data):
        """
        Pre-processes sensor data and runs the Unscented Kalman Filter for state estimation.
        
        Parameters:
        - input_data: Dictionary of sensor data from CARLA
        
        Returns:
        - Dictionary containing preprocessed data for the model
        """
        # Extract and preprocess RGB data
        rgb = input_data['rgb_front'][1][:, :, :3]
        rgb = self.preprocess_rgb(rgb)
        rgb_tensor = torch.from_numpy(rgb).unsqueeze(0).to(self.device, dtype=torch.float32)

        # Extract other sensor data
        gps = input_data['gps'][1][:2]  # Latitude, longitude
        compass = preprocess_compass(input_data['imu'][1][-1])  # Yaw angle
        speed = input_data['speed'][1]['speed']
        result = {
        'rgb': rgb_tensor,
        'compass': compass
        }
        # Convert GPS to CARLA coordinates
        gps_pos = self.route_planner.convert_gps_to_carla(input_data['gps'][1])

        # State estimation with UKF
        if not self.filter_initialized:
            self.ukf.x = np.array([gps_pos[0], gps_pos[1], normalize_angle(compass), speed])  # Initial state
            self.filter_initialized = True
        
        self.ukf.predict(steer=self.control.steer, throttle=self.control.throttle, brake=self.control.brake)
        self.ukf.update(np.array([gps_pos[0], gps_pos[1], normalize_angle(compass), speed]))

        filtered_state = self.ukf.x  # [x, y, yaw, speed]
        self.state_log.append(filtered_state)
        result['gps'] = filtered_state[0:2]
        # Get next waypoint from route planner
        waypoint_route = self.route_planner.run_step(np.append(filtered_state[0:2], gps_pos[2]))

        if len(waypoint_route) > 2:
            target_point, far_command = waypoint_route[1]
            target_point_next, _ = waypoint_route[2]
        elif len(waypoint_route) > 1:
            target_point, far_command = waypoint_route[1]
            target_point_next = target_point
        else:
            target_point, far_command = waypoint_route[0]
            target_point_next = target_point

        if (target_point != self.target_point_prev).all():
            self.target_point_prev = target_point
            self.commands.append(far_command.value)

        one_hot_command = command_to_one_hot(self.commands[-2])
        result['command'] = torch.from_numpy(one_hot_command[np.newaxis]).to(self.device, dtype=torch.float32)

        ego_target_point = inverse_conversion_2d(target_point[:2], result['gps'], result['compass'])  # original

        ego_target_point = torch.from_numpy(ego_target_point[np.newaxis]).to(self.device, dtype=torch.float32)
        result['target_point'] = ego_target_point

        if self.config.two_tp_input:
            ego_target_point_next = inverse_conversion_2d(target_point_next[:2], result['gps'], result['compass'])
            ego_target_point_next = torch.from_numpy(ego_target_point_next[np.newaxis]).to(self.device, dtype=torch.float32)
            result['target_point_next'] = ego_target_point_next
        
        result['speed'] = torch.FloatTensor([speed]).to(self.device, dtype=torch.float32)

        if self.save_path is not None:
            pass
            waypoint_route = self._waypoint_planner.run_step(np.append(result['gps'], gps_pos[2]))
            waypoint_route = extrapolate_waypoint_route(waypoint_route, self.config.route_points)
            route = np.array([[node[0][0], node[0][1]] for node in waypoint_route])[:self.config.route_points]
            self.lon_logger.log_step(route)        

        return result

    @torch.inference_mode()
    def run_step(self, input_data, timestamp, sensors=None):
        """
        Executes the main control loop for each simulation step.
        
        Parameters:
        - input_data: Dictionary of sensor data
        - timestamp: Current simulation time
        - sensors: Optional sensor objects (not used here)
        
        Returns:
        - carla.VehicleControl object with steer, throttle, and brake commands
        """
        self.step += 1

        if not self.initialized:
            self._init()
            control = carla.VehicleControl(steer=0.0, throttle=0.0, brake=1.0)
            self.control = control
            tick_data = self.tick(input_data)
            return control

        # Get preprocessed data from tick
        tick_data = self.tick(input_data)

        # Predict waypoints with CarlaNet
        pred_wp = self.model(tick_data['rgb'], tick_data['target_point'])

        # Compute controls using PID controller
        steer, throttle, brake = self.pid_controller.control_pid(pred_wp, tick_data['speed'])

        # Create and store control command
        control = carla.VehicleControl(steer=steer, throttle=throttle, brake=1.0 if brake else 0.0)
        self.control = control
        return control

    def destroy(self, results=None):
        """
        Cleans up resources after simulation ends.
        
        Parameters:
        - results: Optional simulation results (not used here)
        """
        del self.model
        del self.config



# Filter Functions
def bicycle_model_forward(x, dt, steer, throttle, brake):
  # Kinematic bicycle model.
  # Numbers are the tuned parameters from World on Rails
  front_wb = -0.090769015
  rear_wb = 1.4178275

  steer_gain = 0.36848336
  brake_accel = -4.952399
  throt_accel = 0.5633837

  locs_0 = x[0]
  locs_1 = x[1]
  yaw = x[2]
  speed = x[3]

  if brake:
    accel = brake_accel
  else:
    accel = throt_accel * throttle

  wheel = steer_gain * steer

  beta = math.atan(rear_wb / (front_wb + rear_wb) * math.tan(wheel))
  next_locs_0 = locs_0.item() + speed * math.cos(yaw + beta) * dt
  next_locs_1 = locs_1.item() + speed * math.sin(yaw + beta) * dt
  next_yaws = yaw + speed / rear_wb * math.sin(beta) * dt
  next_speed = speed + accel * dt
  next_speed = next_speed * (next_speed > 0.0)  # Fast ReLU

  next_state_x = np.array([next_locs_0, next_locs_1, next_yaws, next_speed])

  return next_state_x

def state_mean(state, wm):
  '''
    We use the arctan of the average of sin and cos of the angle to calculate
    the average of orientations.
    :param state: array of states to be averaged. First index is the timestep.
    :param wm:
    :return:
    '''
  x = np.zeros(4)
  sum_sin = np.sum(np.dot(np.sin(state[:, 2]), wm))
  sum_cos = np.sum(np.dot(np.cos(state[:, 2]), wm))
  x[0] = np.sum(np.dot(state[:, 0], wm))
  x[1] = np.sum(np.dot(state[:, 1], wm))
  x[2] = math.atan2(sum_sin, sum_cos)
  x[3] = np.sum(np.dot(state[:, 3], wm))

  return x

def inverse_conversion_2d(point, translation, yaw):
  """
  Performs a forward coordinate conversion on a 2D point
  :param point: Point to be converted
  :param translation: 2D translation vector of the new coordinate system
  :param yaw: yaw in radian of the new coordinate system
  :return: Converted point
  """
  rotation_matrix = np.array([[np.cos(yaw), -np.sin(yaw)], [np.sin(yaw), np.cos(yaw)]])

  converted_point = rotation_matrix.T @ (point - translation)
  return converted_point

def measurement_mean(state, wm):
  '''
  We use the arctan of the average of sin and cos of the angle to
  calculate the average of orientations.
  :param state: array of states to be averaged. First index is the
  timestep.
  '''
  x = np.zeros(4)
  sum_sin = np.sum(np.dot(np.sin(state[:, 2]), wm))
  sum_cos = np.sum(np.dot(np.cos(state[:, 2]), wm))
  x[0] = np.sum(np.dot(state[:, 0], wm))
  x[1] = np.sum(np.dot(state[:, 1], wm))
  x[2] = math.atan2(sum_sin, sum_cos)
  x[3] = np.sum(np.dot(state[:, 3], wm))

  return x


def normalize_angle(x):
  x = x % (2 * np.pi)  # force in range [0, 2 pi)
  if x > np.pi:  # move to [-pi, pi)
    x -= 2 * np.pi
  return x

def measurement_function_hx(vehicle_state):
  '''
    For now we use the same internal state as the measurement state
    :param vehicle_state: VehicleState vehicle state variable containing
                          an internal state of the vehicle from the filter
    :return: np array: describes the vehicle state as numpy array.
                       0: pos_x, 1: pos_y, 2: rotatoion, 3: speed
    '''
  return vehicle_state

def command_to_one_hot(command):
  if command < 0:
    command = 4
  command -= 1
  if command not in [0, 1, 2, 3, 4, 5]:
    command = 3
  cmd_one_hot = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
  cmd_one_hot[command] = 1.0

  return np.array(cmd_one_hot)

def residual_state_x(a, b):
  y = a - b
  y[2] = normalize_angle(y[2])
  return y


def residual_measurement_h(a, b):
  y = a - b
  y[2] = normalize_angle(y[2])
  return y

def preprocess_compass(compass):
  """
  Checks the compass for Nans and rotates it into the default CARLA coordinate
  system with range [-pi,pi].
  :param compass: compass value provided by the IMU, in radian
  :return: yaw of the car in radian in the CARLA coordinate system.
  """
  if math.isnan(compass):  # simulation bug
    compass = 0.0
  # The minus 90.0 degree is because the compass sensor uses a different
  # coordinate system then CARLA. Check the coordinate_sytems.txt file
  compass = normalize_angle(compass - np.deg2rad(90.0))

  return compass

class PIDController:
    """PID controller for vehicle control."""
    def __init__(self, k_p=1.0, k_i=0.0, k_d=0.0, n=20):
        self.k_p = k_p
        self.k_i = k_i
        self.k_d = k_d
        self.n = n
        self.error_sum = 0.0
        self.prev_error = 0.0

    def step(self, error):
        self.error_sum += error
        d_term = self.k_d * (error - self.prev_error)
        self.prev_error = error
        return self.k_p * error + self.k_i * self.error_sum + d_term

class VehiclePIDController:
    """Vehicle-specific PID controller adapted from LidarCenterNet."""
    def __init__(self, config):
        self.config = config
        self.turn_controller = PIDController(
            k_p=config.turn_kp, k_i=config.turn_ki, k_d=config.turn_kd, n=config.turn_n
        )
        self.speed_controller = PIDController(
            k_p=config.speed_kp, k_i=config.speed_ki, k_d=config.speed_kd, n=config.speed_n
        )

    def control_pid(self, waypoints, velocity):
        assert waypoints.size(0) == 1, "Batch size must be 1"
        waypoints = waypoints[0].data.cpu().numpy()
        speed = velocity[0].data.cpu().numpy()

        one_second = int(self.config.carla_fps // (self.config.wp_dilation * self.config.data_save_freq))
        half_second = one_second // 2
        num_points = len(waypoints)

        # Adjust indices if waypoints are fewer than expected
        if num_points < one_second:
            one_second = min(one_second, num_points - 1)
            half_second = min(half_second, num_points - 1)
        if half_second < 1:
            half_second = 1
        if one_second < 2:
            one_second = 2

        # Calculate desired speed
        if one_second > half_second:
            desired_speed = np.linalg.norm(waypoints[half_second - 1] - waypoints[one_second - 1]) * 2.0
        else:
            desired_speed = 0.0

        brake = ((desired_speed < self.config.brake_speed) or 
                 ((speed / desired_speed) > self.config.brake_ratio if desired_speed > 0 else False))

        delta = np.clip(desired_speed - speed, 0.0, self.config.clip_delta)
        throttle = self.speed_controller.step(delta)
        throttle = np.clip(throttle, 0.0, self.config.clip_throttle)
        throttle = throttle if not brake else 0.0

        aim_index = min(half_second, len(waypoints) - 1)
        aim = waypoints[aim_index]
        angle = np.degrees(np.arctan2(aim[1], aim[0])) / 90.0
        if speed < 0.01 or brake:
            angle = 0.0
        steer = self.turn_controller.step(angle)
        steer = np.clip(steer, -1.0, 1.0)

        return steer, throttle, brake
    
class EgoModel:
  """
      Kinematic bicycle model describing the motion of a car given it's state and
      action. Tuned parameters are taken from World on Rails.
      """

  def __init__(self, dt, ego_vehicle_model=True):
    self.dt = dt  # the following numbers are optimized for dt=1./20. = 20 FPS

    self.ego_vehicle_model = ego_vehicle_model

    # Kinematic bicycle model. Numbers are the tuned parameters from World
    # on Rails
    self.front_wb = -0.090769015
    self.rear_wb = 1.4178275
    self.steer_gain = 0.36848336
    self.brake_accel = -4.952399
    self.throt_accel = 0.5633837

    # Numbers are tuned parameters for the polynomial equations below using
    # a dataset where the car drives on a straight highway, accelerates to
    # 80 km/h and brakes to 0 km/h
    self.throt_values = np.array([
        9.63873001e-01, 4.37535692e-04, -3.80192912e-01, 1.74950069e+00, 9.16787414e-02, -7.05461530e-02,
        -1.05996152e-03, 6.71079346e-04
    ])
    self.brake_values = np.array([
        9.31711370e-03, 8.20967431e-02, -2.83832427e-03, 5.06587474e-05, -4.90357228e-07, 2.44419284e-09,
        -4.91381935e-12
    ])

  def forward(self, locs, yaws, spds, acts):
    # Kinematic bicycle model. Numbers are the tuned parameters from World
    # on Rails
    steer = acts[..., 0:1].item()
    throt = acts[..., 1:2].item()
    brake = acts[..., 2:3].astype(np.uint8)

    wheel = self.steer_gain * steer

    beta = math.atan(self.rear_wb / (self.front_wb + self.rear_wb) * math.tan(wheel))
    yaws = yaws.item()
    spds = spds.item()
    next_locs_0 = locs[0].item() + spds * math.cos(yaws + beta) * self.dt
    next_locs_1 = locs[1].item() + spds * math.sin(yaws + beta) * self.dt
    next_yaws = yaws + spds / self.rear_wb * math.sin(beta) * self.dt

    if self.ego_vehicle_model:
      if brake:
        spds = spds * 3.6
        features = np.array([spds, spds**2, spds**3, spds**4, spds**5, spds**6, spds**7]).T

        next_spds = (features @ self.brake_values).item() / 3.6
      else:
        throttle = np.clip(throt, 0., 1.0)
        # for a throttle value < 0.3 the car doesn't accelerate and the polynomial model below breaks
        if throttle < 0.3:
          next_spds = spds
        else:
          spds = spds * 3.6
          features = np.array([
              spds, spds**2, throttle, throttle**2, spds * throttle, spds * throttle**2, spds**2 * throttle,
              spds**2 * throttle**2
          ]).T

          next_spds = (features @ self.throt_values).item() / 3.6
    else:
      if brake:
        next_spds = spds + self.brake_accel * self.dt
      else:
        next_spds = spds + self.throt_accel * self.dt

    next_spds = max(0, next_spds)

    next_locs = np.array([next_locs_0, next_locs_1, locs[2]])
    next_yaws = np.array(next_yaws)
    next_spds = np.array(next_spds)

    return next_locs, next_yaws, next_spds